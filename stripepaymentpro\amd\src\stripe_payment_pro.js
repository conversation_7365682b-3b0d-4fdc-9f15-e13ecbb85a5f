// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

import ajax from 'core/ajax';

const { call: fetchMany } = ajax;

// Repository functions for payment flow (keep these for payment functionality)
const applyCoupon = (couponinput, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_applycoupon", args: { couponinput, instanceid } }])[0];

const stripeenrol = (userid, couponid, instanceid) =>
    fetchMany([{ methodname: "moodle_stripepaymentpro_stripe_enrol", args: { userid, couponid, instanceid } }])[0];

const createDOM = (instanceid) => {
    const cache = new Map();
    return {
        getelement(id) {
            const fullid = `${id}-${instanceid}`;
            if (!cache.has(fullid)) {
                cache.set(fullid, document.getElementById(fullid));
            }
            return cache.get(fullid);
        },
        setelement(id, html) {
            const element = this.getelement(id);
            if (element) {
                element.innerHTML = html;
            }
        },
        toggleelement(id, show) {
            const element = this.getelement(id);
            if (element) {
                element.style.display = show ? "block" : "none";
            }
        },
        focuselement(id) {
            const element = this.getelement(id);
            if (element) {
                element.focus();
            }
        },
        setbutton(id, disabled, text, opacity = disabled ? "0.7" : "1") {
            const button = this.getelement(id);
            if (button) {
                button.disabled = disabled;
                button.textContent = text;
                button.style.opacity = opacity;
                button.style.cursor = disabled ? "not-allowed" : "pointer";
            }
        },
    };
};

function stripe_payment_pro(userid, couponid, instanceid,publishablekey, pleasewaitstring, entercoupon, couponappling, paymenterror, paymentgatewaytype) {
    const DOM = createDOM(instanceid);
    let checkoutInstance;
    let isInitialized = false;

    // Cleanup function to destroy existing checkout
    const cleanupCheckout = () => {
        if (checkoutInstance && checkoutInstance.checkout && typeof checkoutInstance.checkout.destroy === 'function') {
            try {
                checkoutInstance.checkout.destroy();
            } catch (e) {
                console.warn("Error destroying checkout:", e);
            }
        }
        checkoutInstance = null;
        isInitialized = false;
    };

    // Function to refresh payment element after coupon application
    const refreshPaymentElement = async () => {
        if (!isInitialized) {
            console.log("Payment element not initialized, skipping refresh");
            return;
        }

        console.log("Refreshing payment element after coupon application...");

        try {
            // Show loading message
            displayMessage("paymentresponse", "Updating payment form with coupon discount...", "info");

            // Cleanup existing checkout
            cleanupCheckout();

            // Re-initialize with updated pricing
            if (initializeElements) {
                await initializeElements();
                clearError("paymentresponse"); // Clear the loading message
            } else {
                throw new Error("Elements initialization function not available");
            }
        } catch (err) {
            console.error("Error refreshing payment element:", err);
            displayMessage("paymentresponse", "Failed to update payment form. Please reload the page.", "error");
        }
    };
    if (typeof window.Stripe === "undefined") {
        // Display an error or log if Stripe.js is not loaded
        displayMessage("paymentresponse", "Stripe.js library not loaded. Please check your template includes.", "error");
        console.error("Stripe.js not loaded.");
        return;
    }

    const displayMessage = (containerid, message, type) => {
        let color;
        switch (type) {
            case "error": color = "red"; break;
            case "success": color = "green"; break;
            case "info": color = "blue"; break;
            default: color = "blue"; break;
        }
        DOM.setelement(containerid, `<p style="color: ${color}; font-weight: bold;">${message}</p>`);
        DOM.toggleelement(containerid, true);
    };

    const clearError = (containerId) => {
        DOM.setelement(containerId, "");
        DOM.toggleelement(containerId, false);
    };

    const updateUIFromServerResponse = (data) => {
        console.log("Updating UI from server response:", data); // Debug log
        console.log("Current payment gateway type:", paymentgatewaytype); // Debug log
        console.log("Is initialized:", isInitialized); // Debug log

        if (data.message) {
            displayMessage("showmessage", data.message, data.uistate === "error" ? "error" : "success");
        } else {
            clearError("showmessage");
        }

        // Handle button visibility based on payment gateway type and state
        if (paymentgatewaytype === "elements") {
            console.log("Elements mode - UI state:", data.uistate); // Debug log
            // For Elements mode, only show enrol button when course is completely free
            if (data.uistate === "paid") {
                console.log("Course is free, showing enrol button"); // Debug log
                // Course is free, show enrol button and hide load button
                DOM.toggleelement("enrolbutton", true);
                DOM.toggleelement("buynow", true);
                DOM.toggleelement("load-payment-form", false);
            } else {
                console.log("Course requires payment, keeping Elements UI"); // Debug log
                // Course still requires payment (discount or error state), keep Elements mode UI
                DOM.toggleelement("enrolbutton", false);
                DOM.toggleelement("buynow", false);
                // Don't change load-payment-form visibility here - handle it separately
            }
        } else {
            console.log("Checkout mode - UI state:", data.uistate); // Debug log
            // Checkout mode - show enrol button when payment is required
            DOM.toggleelement("enrolbutton", data.uistate === "paid");
        }
        // Show total section when payment is required (paid state means free enrollment)
        DOM.toggleelement("total", data.uistate === "paid" || data.uistate === "discount");

        if (data.uistate !== "error") {
            DOM.toggleelement("discountsection", data.showsections.discountsection);

            if (data.showsections.discountsection) {
                if (data.couponname) {
                    DOM.setelement("discounttag", data.couponname);
                }
                if (data.discountamount) {
                    // Use currency symbol ($ for USD) instead of currency code
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    DOM.setelement("discountamountdisplay", `-${currencySymbol}${data.discountamount}`);
                }
                if (data.discountamount && data.discountvalue) {
                    const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                    let note = data.coupontype === "percentoff"
                        ? `${data.discountvalue}% off`
                        : `${currencySymbol}${data.discountvalue} off`;

                    // Add duration information if available
                    if (data.couponduration) {
                        if (data.couponduration === "repeating" && data.coupondurationmonths) {
                            note += ` Expires in ${data.coupondurationmonths} months`;
                        } else if (data.couponduration !== "once") {
                            note += ` ${data.couponduration}`;
                        }
                    }

                    DOM.setelement("discountnote", note);
                }
            }

            if (data.status && data.currency) {
                const currencySymbol = data.currency === 'USD' ? '$' : data.currency + ' ';
                const formattedAmount = `${currencySymbol}${parseFloat(data.status).toFixed(2)}`;

                // Update main price display
                const mainprice = DOM.getelement("mainprice");
                if (mainprice) {
                    mainprice.textContent = formattedAmount;
                }

                // Update total amount display
                const totalamount = DOM.getelement("totalamount");
                if (totalamount) {
                    totalamount.textContent = formattedAmount;
                }

                // Update recurring price display if discount applies to recurring payments
                if (data.discounted_renewal_fee !== undefined && data.original_renewal_fee !== undefined) {
                    const discountedRenewalFee = parseFloat(data.discounted_renewal_fee);
                    const originalRenewalFee = parseFloat(data.original_renewal_fee);

                    if (discountedRenewalFee !== originalRenewalFee) {
                        console.log(`Updating recurring price from ${originalRenewalFee} to ${discountedRenewalFee}`);

                        // Update specific recurring price elements
                        const recurringPriceElements = [
                            DOM.getelement(`recurring-price-heading`),
                            DOM.getelement(`recurring-price-breakdown`),
                        ];

                        recurringPriceElements.forEach(element => {
                            if (element && element.textContent.includes(currencySymbol + originalRenewalFee.toFixed(2))) {
                                element.textContent = element.textContent.replace(
                                    currencySymbol + originalRenewalFee.toFixed(2),
                                    currencySymbol + discountedRenewalFee.toFixed(2)
                                );
                                console.log(`Updated recurring price element: ${element.textContent}`);
                            }
                        });

                        // Also update any elements that contain "Then" text pattern
                        const allElements = document.querySelectorAll('*');
                        allElements.forEach(element => {
                            if (element.textContent && element.textContent.includes('Then ' + currencySymbol + originalRenewalFee.toFixed(2))) {
                                element.textContent = element.textContent.replace(
                                    'Then ' + currencySymbol + originalRenewalFee.toFixed(2),
                                    'Then ' + currencySymbol + discountedRenewalFee.toFixed(2)
                                );
                                console.log(`Updated "Then" text: ${element.textContent}`);
                            }
                        });
                    }
                }

                // Handle Elements mode after coupon application
                if (paymentgatewaytype === "elements") {
                    console.log("Handling Elements mode after coupon, state:", data.uistate); // Debug log
                    if (data.uistate === "paid") {
                        // Course is now free - this is already handled in updateUIFromServerResponse
                        // Cleanup any existing payment form
                        if (isInitialized) {
                            cleanupCheckout();
                        }
                    } else if (data.uistate === "discount" || data.uistate === "error") {
                        // Course still requires payment (with discount or error)
                        if (isInitialized) {
                            console.log("Payment form loaded, refreshing with coupon"); // Debug log
                            // If payment form is already loaded, refresh it
                            refreshPaymentElement();
                        } else {
                            console.log("Payment form not loaded, showing load button"); // Debug log
                            // If payment form is not loaded, ensure load button is visible
                            DOM.toggleelement("load-payment-form", true);
                        }
                    }
                }
            }
        }
    };

    const applyCouponHandler = async (event) => {
        event.preventDefault();
        const couponinput = DOM.getelement("coupon");
        const couponcode = couponinput?.value.trim();
        if (!couponcode) {
            displayMessage("showmessage", entercoupon, "error");
            DOM.focuselement("coupon");
            return;
        }
        DOM.setbutton("apply", true, couponappling);
        try {
            const data = await applyCoupon(couponcode, instanceid);
            console.log("Coupon application response:", data); // Debug log
            console.log("Current payment gateway type:", paymentgatewaytype); // Debug log
            if (data?.status !== undefined) {
                couponid = couponcode;
                DOM.toggleelement("coupon", false);
                DOM.toggleelement("apply", false);
                updateUIFromServerResponse(data);
            } else {
                throw new Error("Invalid server response");
            }
        } catch (error) {
            console.error("Coupon application error:", error); // Debug log
            displayMessage("showmessage", error.message || "Coupon validation failed", "error");
            DOM.focuselement("coupon");
        } finally {
            DOM.setbutton("apply", false, "Apply"); // Ensure button is re-enabled
        }
    };

    const EnrollHandler = async () => {
        const enrollbutton = DOM.getelement("enrolbutton");
        if (!enrollbutton) return;
        clearError("paymentresponse");
        DOM.setbutton("enrolbutton", true, pleasewaitstring);

        try {
            // Handle Checkout mode (Elements mode uses embedded checkout which handles submission automatically)
            const paymentdata = await stripeenrol(userid, couponid, instanceid);
            if (paymentdata.error?.message) {
                displayMessage("paymentresponse", paymentdata.error.message, "error");
            } else if (paymentdata.status === "success" && paymentdata.redirecturl) {
                window.location.href = paymentdata.redirecturl;
            } else {
                displayMessage("paymentresponse", "Unknown error occurred during payment.", "error");
            }
        } catch (err) {
            displayMessage("paymentresponse", err.message, "error");
        } finally {
            DOM.toggleelement("enrolbutton", false);
        }
    };

    // Define initializeElements function globally so it can be called from anywhere
    let initializeElements;

    const loadPaymentFormHandler = async () => {
        const loadButton = DOM.getelement("load-payment-button");
        if (!loadButton) return;

        DOM.setbutton("load-payment-button", true, "Loading...");

        try {
            // Initialize the Elements form
            if (initializeElements) {
                await initializeElements();
            } else {
                throw new Error("Elements initialization function not available");
            }

            // Hide the load button and show the payment form
            DOM.toggleelement("load-payment-form", false);

        } catch (err) {
            console.error("Error loading payment form:", err);
            displayMessage("paymentresponse", "Failed to load payment form. Please try again.", "error");
            DOM.setbutton("load-payment-button", false, "Load Payment Form");
        }
    };

    const setupEventListeners = () => {
        const elements = [
            { id: "apply", event: "click", handler: applyCouponHandler },
            { id: "enrolbutton", event: "click", handler: EnrollHandler },
            { id: "load-payment-button", event: "click", handler: loadPaymentFormHandler },
        ];
        elements.forEach(({ id, event, handler }) => {
            const element = DOM.getelement(id);
            if (element) {
                element.addEventListener(event, handler);
            }
        });
    };

    if (paymentgatewaytype === "elements") {
        // Hide the Buy Now button and show Load Payment Form button for Elements mode
        DOM.toggleelement("enrolbutton", false);
        DOM.toggleelement("buynow", false);
        DOM.toggleelement("load-payment-form", true);

        const stripe = window.Stripe(publishablekey);

        // Assign the function to the global variable
        initializeElements = async () => {
            // Prevent multiple initializations
            if (isInitialized) {
                console.log("Embedded checkout already initialized, skipping...");
                return;
            }

            // Cleanup any existing checkout first
            cleanupCheckout();

            const paymentelementContainer = DOM.getelement("payment-element");
            if (!paymentelementContainer) {
                displayMessage("paymentresponse", "Payment element container (ID: payment-element) not found in HTML. Check your template.", "error");
                return;
            }
            clearError("paymentresponse"); // Clear any previous errors before initializing

            try {
                // Fetch client secret for embedded checkout
                const fetchClientSecret = async () => {
                    const response = await stripeenrol(userid, couponid, instanceid);

                    console.log("Stripe enrol response:", response); // Debug log

                    if (response.error && response.error.message) {
                        throw new Error(response.error.message);
                    }

                    if (response.paymentintent) {
                        console.log("PaymentIntent data:", response.paymentintent); // Debug log
                        // Try to parse the paymentintent field
                        try {
                            const data = typeof response.paymentintent === 'string'
                                ? JSON.parse(response.paymentintent)
                                : response.paymentintent;
                            console.log("Extracted client_secret:", data.client_secret); // Debug log
                            return data.client_secret;
                        } catch (parseError) {
                            console.error("Failed to parse paymentintent:", parseError);
                            console.error("PaymentIntent raw data:", response.paymentintent);
                            throw new Error("Invalid payment data received from server.");
                        }
                    }

                    throw new Error("No client secret found in response");
                };

                // Clear the container before mounting
                paymentelementContainer.innerHTML = '';

                // Initialize embedded checkout
                const checkout = await stripe.initEmbeddedCheckout({
                    fetchClientSecret: fetchClientSecret,
                });

                // Mount the embedded checkout
                checkout.mount(`#${paymentelementContainer.id}`);

                // Store the checkout instance
                checkoutInstance = { stripe, checkout };

                // Mark as initialized to prevent multiple initializations
                isInitialized = true;

                // Show the payment element container and hide buy now button
                DOM.toggleelement("payment-element", true);
                DOM.toggleelement("buynow", false);

            } catch (err) {
                console.error("Stripe Elements initialization error:", err);
                displayMessage("paymentresponse", err.message || "Stripe initialization error. Check console.", "error");
                // Reset initialization flag on error so user can retry
                isInitialized = false;
            }
        };
        // Don't auto-initialize - wait for user to click "Load Payment Form"
    } else {
        // If not 'elements' mode, ensure the original enrol button is visible and functional
        DOM.toggleelement("enrolbutton", true);
        DOM.setbutton("enrolbutton", false, "Buy Now"); // Ensure it's enabled
        // Hide payment element container for checkout mode
        DOM.toggleelement("payment-element", false);
        DOM.toggleelement("buynow", true);
    }

    setupEventListeners();
}

/**
 * Initialize coupon settings for the coupon management page
 */
const initCouponSettings = () => {
    console.log('Coupon settings initialized');

    // Add search functionality only (deletion is handled by template functions)
    const tableContainer = document.querySelector('.table-responsive');
    if (tableContainer && !document.querySelector('#coupon-search')) {
        const searchContainer = document.createElement('div');
        searchContainer.style.marginBottom = '15px';
        searchContainer.innerHTML = `
            <input type="text" id="coupon-search" placeholder="Search coupons..."
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px;">
        `;
        tableContainer.parentNode.insertBefore(searchContainer, tableContainer);

        document.getElementById('coupon-search').addEventListener('input', (event) => {
            const searchTerm = event.target.value.toLowerCase();
            document.querySelectorAll('.table tbody tr').forEach(row => {
                row.style.display = row.textContent.toLowerCase().includes(searchTerm) ? '' : 'none';
            });
        });
    }
};

export default {
    stripe_payment_pro,
    initCouponSettings
};
