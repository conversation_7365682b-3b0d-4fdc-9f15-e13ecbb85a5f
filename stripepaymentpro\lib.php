<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Stripe Pro enrolment plugin.
 *
 * This plugin allows you to set up paid courses with subscription support.
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

use core\output\local\properties\button;
defined('MOODLE_INTERNAL') || die();

use core_enrol\output\enrol_page;

global $CFG;
require_once($CFG->dirroot.'/lib/adminlib.php');
require_once($CFG->dirroot.'/enrol/stripepayment/lib.php');
require_once($CFG->dirroot . '/enrol/stripepayment/vendor/stripe/stripe-php/init.php');

use \Stripe\Price as Price;
use \Stripe\Product as Product;
use \Stripe\Stripe as Stripe;
/**
 * Stripe enrolment plugin implementation.
 * 
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class enrol_stripepaymentpro_plugin extends enrol_stripepayment_plugin {
    /**
     * Lists all renewalintarval available for plugin.
     * @return $renewalintarval
     */
    public function get_renewalintarval() {
        $times = array(
             'day', 'week', 'month', 'year'
         );
        return $times;
    }
     /**
     * Sets up navigation entries.
     *
     * @param navigation_node $instancesnode
     * @param stdClass $instance
     * @return void
     */
    public function add_course_navigation($instancesnode, stdClass $instance) {
        if ($instance->enrol !== 'stripepaymentpro') {
             throw new coding_exception('Invalid enrol instance type!');
        }
        $context = context_course::instance($instance->courseid);
        if (has_capability('enrol/stripepaymentpro:manage', $context)) {
            $managelink = new moodle_url('/enrol/editinstance.php',
            ['courseid' => $instance->courseid, 'id' => $instance->id, 'type' => 'stripepaymentpro']);
            $instancesnode->add($this->get_instance_name($instance), $managelink, navigation_node::TYPE_SETTING);
        }
    }
    /**
     * Returns edit icons for the page with list of instances
     * @param stdClass $instance
     * @return array
     */
    public function get_action_icons(stdClass $instance) {
        global $OUTPUT;
        if ($instance->enrol !== 'stripepaymentpro') {
            throw new coding_exception('invalid enrol instance!');
        }
        $context = context_course::instance($instance->courseid);
        $icons = array();
        if (has_capability('enrol/stripepaymentpro:manage', $context)) {
            $linkparams = [
                'courseid' => $instance->courseid,
                'id' => $instance->id,
                'type' => $instance->enrol,
            ];
            $editlink = new moodle_url('/enrol/editinstance.php', $linkparams);
            $icon = new pix_icon('t/edit', get_string('edit'), 'core', ['class' => 'iconsmall']);
            $icons[] = $OUTPUT->action_icon($editlink, $icon);
        }
        return $icons;
    }
    /**
     * Returns optional enrolment information icons.
     *
     * This is used in course list for quick overview of enrolment options.
     *
     * We are not using single instance parameter because sometimes
     * we might want to prevent icon repetition when multiple instances
     * of one type exist. One instance may also produce several icons.
     *
     * @param array $instances all enrol instances of this type in one course
     * @return array of pix_icon
     */
    public function get_info_icons(array $instances) {
        $found = false;
        foreach ($instances as $instance) {
            if ($instance->enrolstartdate != 0 && $instance->enrolstartdate > time()) {
                continue;
            }
            if ($instance->enrolenddate != 0 && $instance->enrolenddate < time()) {
                continue;
            }
            $found = true;
            break;
        }
        if ($found) {
            return [new pix_icon('icon', get_string('pluginname', 'enrol_stripepaymentpro'), 'enrol_stripepaymentpro')];
        }
        return [];
    }

    /**
     * Returns true if the user can add a new instance in this course.
     * @param int $courseid
     * @return boolean
     */
    public function can_add_instance($courseid) {
        $context = context_course::instance($courseid, MUST_EXIST);

        if (!has_capability('moodle/course:enrolconfig', $context) or !has_capability('enrol/stripepaymentpro:manage', $context)) {
            return false;
        }

        // Multiple instances supported - different cost for different roles and subscription periods.
        return true;
    }

    /**
     * Returns link to page which may be used to add new instance of enrolment plugin in course.
     * @param int $courseid
     * @return moodle_url page url
     */
    public function get_newinstance_link($courseid) {
        $context = context_course::instance($courseid, MUST_EXIST);
        if (!has_capability('moodle/course:enrolconfig', $context) or !has_capability('enrol/stripepaymentpro:manage', $context)) {
            return null;
        }
        // Multiple instances supported - different cost for different roles.
        return new moodle_url('/enrol/editinstance.php', ['courseid' => $courseid, 'type' => 'stripepaymentpro']);
    }
    /**
     * We are a good plugin and don't invent our own UI/validation code path.
     *
     * @return boolean
     */
    public function use_standard_editing_ui() {
        return true;
    }

    /**
     * Add elements to the edit instance form.
     *
     * @param stdClass $instance
     * @param MoodleQuickForm $mform
     * @param context $context
     * @return bool
     */
    public function edit_instance_form($instance, MoodleQuickForm $mform, $context) {
        \Stripe\Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));
        // Call parent method first to get basic fields
        parent::edit_instance_form($instance, $mform, $context);

        // Add recurring product checkbox
        $mform->addElement('checkbox', 'recurringproduct', get_string('recurringcost', 'enrol_stripepaymentpro'));
        // Set checkbox based on whether instance has recurring pricing
        $has_recurring = isset($instance->customtext4) && !empty($instance->customtext4) && $instance->customtext4 !== '0';
        $mform->setDefault('recurringproduct', $has_recurring ? 1 : 0);

        // Recurring Cost
        $mform->addElement('text', 'customtext4', get_string('recurringcost', 'enrol_stripepaymentpro'));
        $mform->setType('customtext4', PARAM_FLOAT);

        // Get recurring cost amount safely
        $recurring_cost_amount = '';
        if (isset($instance->customtext4) && !empty($instance->customtext4) && $instance->customtext4 !== '0') {
            try {
                // Ensure Stripe API key is set before making the call
                $secretkey = get_config('enrol_stripepayment', 'secretkey');
                if (!empty($secretkey)) {
                    \Stripe\Stripe::setApiKey($secretkey);
                    $recurring_price = $this->safe_price_retrieve($instance->customtext4);
                    if ($recurring_price && isset($recurring_price->unit_amount)) {
                        // Use instance currency or fallback to USD
                        $currency = !empty($instance->currency) ? $instance->currency : 'USD';
                        $recurring_cost_amount = $recurring_price->unit_amount / $this->get_fractional_unit_amount($currency);
                        debugging('Retrieved recurring cost amount: ' . $recurring_cost_amount . ' for price ID: ' . $instance->customtext4, DEBUG_DEVELOPER);
                    } else {
                        debugging('Could not retrieve recurring price object for ID: ' . $instance->customtext4, DEBUG_DEVELOPER);
                    }
                } else {
                    debugging('Stripe secret key not configured', DEBUG_DEVELOPER);
                }
            } catch (Exception $e) {
                // If we can't retrieve the price, leave the field empty
                debugging('Could not retrieve recurring price ' . $instance->customtext4 . ': ' . $e->getMessage(), DEBUG_DEVELOPER);
            }
        }

        $mform->setDefault('customtext4', $recurring_cost_amount);
        $mform->disabledIf('customtext4', 'recurringproduct', 'notchecked');

        // Product ID (read-only)
        $mform->addElement('static', 'customtext2', get_string('productid', 'enrol_stripepaymentpro'));
        $mform->setDefault('customtext2', 'This will fill automatically');

        // Renewal interval
        $renewalintarval = $this->get_renewalintarval();
        $mform->addElement('select', 'customint4', get_string('renewalintarval', 'enrol_stripepaymentpro'), $renewalintarval);
        $mform->disabledIf('customint4', 'recurringproduct', 'notchecked');

        // Renewal interval number
        $mform->addElement('text', 'customint1', get_string('renewalintarvalnum', 'enrol_stripepaymentpro'));
        $mform->setDefault('customint1', '7');
        $mform->setType('customint1', PARAM_RAW);
        $mform->addHelpButton('customint1', 'renewalintarvalnum', 'enrol_stripepaymentpro');
        $mform->disabledIf('customint1', 'recurringproduct', 'notchecked');

        // Trial Period
        $units = array(DAYSECS);
        $mform->addElement('duration', 'customint2', get_string('trialperiod', 'enrol_stripepaymentpro'),
            array('optional' => true, 'defaultunit' => 86400, 'units' => $units));
        $mform->setDefault('trialperiod', 'customint2');
    }

    /**
     * Perform custom validation of the data used to edit the instance.
     *
     * @param array $data array of ("fieldname"=>value) of submitted data
     * @param array $files array of uploaded files "element_name"=>tmp_file_path
     * @param object $instance The instance loaded from the DB
     * @param context $context The context of the instance we are editing
     * @return array of "element_name"=>"error_description" if there are errors,
     *         or an empty array if everything is OK.
     */
    public function edit_instance_validation($data, $files, $instance, $context) {
        $errors = parent::edit_instance_validation($data, $files, $instance, $context);

        // Validate recurring cost if recurring product is enabled
        if (!empty($data['recurringproduct']) && !empty($data['customtext4'])) {
            $customtext4 = str_replace(get_string('decsep', 'langconfig'), '.', $data['customtext4']);
            if (!is_numeric($customtext4)) {
                $errors['customtext4'] = get_string('costerror', 'enrol_stripepaymentpro');
            }
        }

        // Validate renewal interval number
        if (!empty($data['recurringproduct']) && !empty($data['customint1'])) {
            if (!is_numeric($data['customint1']) || $data['customint1'] <= 0) {
                $errors['customint1'] = get_string('renewalintarvalnumerror', 'enrol_stripepaymentpro');
            }
        }

        return $errors;
    }

    /**
     * Add new instance of enrol plugin.
     * @param object $course
     * @param array $fields instance fields
     * @return int id of new instance, null if can not be created
     */
    public function add_instance($course, ?array $fields = null) {
    global $DB;

    if ($fields && !empty($fields['cost'])) {
        $fields['cost'] = unformat_float($fields['cost']);
    }
    if ($fields && !empty($fields['customtext4'])) {
        $fields['customtext4'] = unformat_float($fields['customtext4']);
    }

    // Set up Stripe API
    \Stripe\Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));

    try {
        $productid = null;

        // Check if course already has a Stripe product ID
        $existing = $DB->get_record('enrol', [
            'enrol' => 'stripepaymentpro',
            'courseid' => $course->id
        ], 'customtext2', IGNORE_MULTIPLE);

        if ($existing && !empty($existing->customtext2)) {
            $productid = $existing->customtext2;

            // Verify if the product still exists in Stripe (optional, safer)
            try {
                $product = \Stripe\Product::retrieve($productid);
            } catch (\Exception $e) {
                debugging('Existing Stripe product not found: ' . $e->getMessage(), DEBUG_DEVELOPER);
                $product = null;
                $productid = null;
            }
        }

        // If product doesn't exist, create a new one
        if (!$productid) {
            $product = \Stripe\Product::create([
                'name' => $course->fullname,
                'description' => 'Course enrolment for ' . $course->fullname,
            ]);
            $productid = $product->id;
        }

        // Create initial one-time price
        $initial_amount = $fields['cost'] * $this->get_fractional_unit_amount($fields['currency']);
        $initial_price = \Stripe\Price::create([
            'unit_amount' => $initial_amount,
            'currency' => $fields['currency'],
            'product' => $productid,
        ]);

        // Store product and initial price IDs
        $fields['customtext2'] = $productid; // Product ID
        $fields['customtext3'] = $initial_price->id; // Initial price ID

        // Handle recurring pricing if enabled
        if (!empty($fields['recurringproduct']) && !empty($fields['customtext4'])) {
            $recurring_amount = $fields['customtext4'] * $this->get_fractional_unit_amount($fields['currency']);
            $intervals = $this->get_renewalintarval();

            $recurring_price = \Stripe\Price::create([
                'unit_amount' => $recurring_amount,
                'currency' => $fields['currency'],
                'recurring' => [
                    'interval' => $intervals[$fields['customint4']],
                    'interval_count' => $fields['customint1'],
                ],
                'product' => $productid,
            ]);

            $fields['customtext4'] = $recurring_price->id; // Recurring price ID
            $fields['customtext1'] = $intervals[$fields['customint4']]; // Interval
        } else {
            $fields['customtext4'] = 0;
            $fields['customtext1'] = 0;
        }

        // Update default price for product (optional)
        \Stripe\Product::update($productid, [
            'default_price' => $initial_price->id,
        ]);

    } catch (Exception $e) {
        debugging('Stripe integration failed: ' . $e->getMessage(), DEBUG_DEVELOPER);
    }

    return parent::add_instance($course, $fields);
}


    /**
     * Update instance of enrol plugin.
     * @param stdClass $instance
     * @param stdClass $data modified instance fields
     * @return boolean
     */
    public function update_instance($instance, $data) {
        global $DB;

        if ($data) {
            $data->cost = unformat_float($data->cost);
            if (isset($data->customtext4)) {
                $data->customtext4 = unformat_float($data->customtext4);
            }

            // Set up Stripe API
            Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));

            // Handle Stripe product and pricing updates
            if ($instance->customtext2) {
                try {
                    // Check if pricing data has actually changed
                    $pricing_changed = $this->has_pricing_changed($instance, $data);

                    if ($pricing_changed) {
                        debugging('Pricing changes detected, creating new Stripe prices', DEBUG_DEVELOPER);

                        // Update existing product
                        $product = \Stripe\Product::retrieve($instance->customtext2);

                        // Create new initial price only if changed
                        $initial_amount = $data->cost * $this->get_fractional_unit_amount($data->currency);
                        $initial_price = Price::create([
                            'unit_amount' => $initial_amount,
                            'currency' => $data->currency,
                            'product' => $product->id,
                        ]);

                        debugging('Created new initial price: ' . $initial_price->id, DEBUG_DEVELOPER);

                        // Handle recurring pricing
                        if (!empty($data->recurringproduct) && !empty($data->customtext4) && $data->customtext4 !== '0') {
                            $recurring_amount = $data->customtext4 * $this->get_fractional_unit_amount($data->currency);
                            $interval = $this->get_renewalintarval();

                            $recurring_price = Price::create([
                                'unit_amount' => $recurring_amount,
                                'currency' => $data->currency,
                                'recurring' => [
                                    'interval' => $interval[$data->customint4],
                                    'interval_count' => $data->customint1,
                                ],
                                'product' => $product->id,
                            ]);

                            debugging('Created new recurring price: ' . $recurring_price->id, DEBUG_DEVELOPER);

                            $data->customtext4 = $recurring_price->id;
                            $data->customtext1 = $interval[$data->customint4];
                        } else {
                            // Recurring product is disabled
                            $data->customtext4 = 0;
                            $data->customtext1 = 0;
                        }

                        // Update product with new default price
                        \Stripe\Product::update($product->id, [
                            'default_price' => $initial_price->id,
                        ]);

                        $data->customtext3 = $initial_price->id;
                    } else {
                        debugging('No pricing changes detected, keeping existing price IDs', DEBUG_DEVELOPER);
                        // No changes detected, keep existing price IDs
                        $data->customtext3 = $instance->customtext3;
                        $data->customtext4 = $instance->customtext4;
                        $data->customtext1 = $instance->customtext1;
                    }
                } catch (Exception $e) {
                    debugging('Error updating Stripe pricing: ' . $e->getMessage(), DEBUG_DEVELOPER);
                    // On error, keep existing values to prevent data loss
                    $data->customtext3 = $instance->customtext3;
                    $data->customtext4 = $instance->customtext4;
                    $data->customtext1 = $instance->customtext1;
                }
            }
        }

        return parent::update_instance($instance, $data);
    }

    /**
     * Check if pricing data has changed between current instance and new data
     * @param stdClass $instance Current instance data
     * @param stdClass $data New data to be saved
     * @return bool True if pricing has changed, false otherwise
     */
    private function has_pricing_changed($instance, $data) {
        // Debug logging to help identify issues
        debugging('Checking pricing changes for instance ' . $instance->id, DEBUG_DEVELOPER);
        debugging('Old cost: ' . $instance->cost . ', New cost: ' . $data->cost, DEBUG_DEVELOPER);
        debugging('Old currency: ' . $instance->currency . ', New currency: ' . $data->currency, DEBUG_DEVELOPER);

        // Check if initial cost has changed
        if (abs((float)$instance->cost - (float)$data->cost) > 0.001) {
            debugging('Initial cost changed', DEBUG_DEVELOPER);
            return true;
        }

        // Check if currency has changed
        if ($instance->currency !== $data->currency) {
            debugging('Currency changed', DEBUG_DEVELOPER);
            return true;
        }

        // Check if recurring product setting has changed
        $old_recurring = !empty($instance->customtext4) && $instance->customtext4 !== '0';
        // Handle checkbox properly - if not checked, recurringproduct won't be in $data
        $new_recurring = !empty($data->recurringproduct) && !empty($data->customtext4) && $data->customtext4 !== '0';

        debugging('Old recurring: ' . ($old_recurring ? 'true' : 'false') . ', New recurring: ' . ($new_recurring ? 'true' : 'false'), DEBUG_DEVELOPER);

        if ($old_recurring !== $new_recurring) {
            debugging('Recurring setting changed', DEBUG_DEVELOPER);
            return true;
        }

        // If both have recurring pricing, check if recurring amount has changed
        if ($new_recurring) {
            // Get current recurring price from Stripe to compare
            Stripe::setApiKey(get_config('enrol_stripepayment', 'secretkey'));
            $current_recurring_price = $this->safe_price_retrieve($instance->customtext4);

            if (!$current_recurring_price) {
                // If we can't retrieve the current price, assume it has changed
                debugging('Could not retrieve current recurring price', DEBUG_DEVELOPER);
                return true;
            }

            $current_recurring_amount = $current_recurring_price->unit_amount / $this->get_fractional_unit_amount($instance->currency);

            debugging('Old recurring amount: ' . $current_recurring_amount . ', New recurring amount: ' . $data->customtext4, DEBUG_DEVELOPER);

            if (abs($current_recurring_amount - (float)$data->customtext4) > 0.001) {
                debugging('Recurring amount changed', DEBUG_DEVELOPER);
                return true;
            }

            // Check if interval has changed
            $intervals = $this->get_renewalintarval();
            $new_interval = isset($intervals[$data->customint4]) ? $intervals[$data->customint4] : 'month';
            if ($current_recurring_price->recurring->interval !== $new_interval) {
                debugging('Recurring interval changed', DEBUG_DEVELOPER);
                return true;
            }

            // Check if interval count has changed
            if ($current_recurring_price->recurring->interval_count !== (int)$data->customint1) {
                debugging('Recurring interval count changed', DEBUG_DEVELOPER);
                return true;
            }
        }

        // Check if trial period has changed
        if ((int)$instance->customint2 !== (int)$data->customint2) {
            debugging('Trial period changed', DEBUG_DEVELOPER);
            return true;
        }

        debugging('No pricing changes detected', DEBUG_DEVELOPER);
        return false;
    }

    /**
     * Creates course enrol form, checks if form submitted
     * and enrols user if necessary. It can also redirect.
     *
     * @param stdClass $instance
     * @return string html text, usually a form in a text box
     */
    public function enrol_page_hook(stdClass $instance) {
        global $CFG, $USER, $OUTPUT, $DB, $PAGE;

        $plugin = enrol_get_plugin('stripepaymentpro');
        $plugincorecore = enrol_get_plugin('stripepayment');

        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        if (empty($secretkey)) {
            return '<p>Stripe configuration incomplete. Please contact administrator.</p>';
        }

        Stripe::setApiKey($secretkey);

        // Check if prices need recreation due to API key mode change
        if ($this->prices_need_recreation($instance)) {
            if ($this->recreate_prices_for_instance($instance)) {
                // Reload instance with updated price IDs
                $instance = $DB->get_record('enrol', ['id' => $instance->id]);
            } else {
                return '<p>Unable to initialize payment system. Please contact administrator.</p>';
            }
        }

        $enrolstatus = $this->can_stripepayment_enrol($instance);
        if (!$enrolstatus) {
            $notification = new \core\output\notification(get_string('maxenrolledreached', 'enrol_stripepaymentpro'), 'error', false);
            $notification->set_extra_classes(['mb-0']);
            $enrolpage = new enrol_page(
                instance: $instance,
                header: $this->get_instance_name($instance),
                body: $OUTPUT->render($notification));
            return $OUTPUT->render($enrolpage);
        }

        if ($DB->record_exists('user_enrolments', ['userid' => $USER->id, 'enrolid' => $instance->id])) {
            return '';
        }

        if ($instance->enrolstartdate != 0 && $instance->enrolstartdate > time()) {
            return '';
        }

        if ($instance->enrolenddate != 0 && $instance->enrolenddate < time()) {
            return '';
        }

        $course = $DB->get_record('course', array('id' => $instance->courseid));
        $context = context_course::instance($course->id);
        $shortname = format_string($course->shortname, true, array('context' => $context));

        // Get currency symbol
        $currency_symbol = $plugincorecore->show_currency_symbol(strtolower($instance->currency));

        // Get product and pricing information
        $productid = $instance->customtext2;
        if (!$productid) {
            return '<p>'.get_string('nocost', 'enrol_stripepaymentpro').'</p>';
        }

        try {
            $price = $this->safe_price_retrieve($instance->customtext3);
            if (!$price) {
                return '<p>Unable to retrieve pricing information. Please contact administrator.</p>';
            }
            $sign_up_fee = $price->unit_amount / $plugin->get_fractional_unit_amount($instance->currency);

            // Handle recurring pricing
            $renewal_fee = 0;
            $interval = '';
            $interval_count = 0;
            $trial_period_days = 0;

            if ($instance->customtext4 > 0) {
                $renewalcostobject = $this->safe_price_retrieve($instance->customtext4);
                if ($renewalcostobject) {
                    $renewal_fee = $renewalcostobject->unit_amount / $plugin->get_fractional_unit_amount($instance->currency);
                    $interval = $renewalcostobject->recurring->interval;
                    $interval_count = $renewalcostobject->recurring->interval_count;
                }
                $trial_period_days = $instance->customint2 ? $instance->customint2 / 86400 - 1 : 0;

                // Calculate next recurring date
                $today = new DateTime();
                $intervalspec = 'P';
                switch ($interval) {
                    case 'day':
                        $intervalspec .= $interval_count . 'D';
                        break;
                    case 'week':
                        $intervalspec .= ($interval_count * 7) . 'D';
                        break;
                    case 'month':
                        $intervalspec .= $interval_count . 'M';
                        break;
                    case 'year':
                        $intervalspec .= $interval_count . 'Y';
                        break;
                }

                $intervalobject = new DateInterval($intervalspec);
                $nextrecurringdate = clone $today;
                if ($trial_period_days > 0) {
                    $nextrecurringdate->add(new DateInterval('P' . ($trial_period_days + 1) . 'D'));
                } else {
                    $nextrecurringdate->add($intervalobject);
                }
            }

        } catch (Exception $e) {
            return '<p>Error retrieving product information.</p>';
        }

        // Calculate subtotal and total cost
        $subtotal = $instance->customtext4 > 0 ? $renewal_fee + $sign_up_fee : $sign_up_fee;
        $total_cost = $subtotal; // Default total cost (no discount applied in display)

        if (abs($total_cost) < 0.01) { // No cost, other enrolment methods (instances) should be used.
            return '<p>'.get_string('nocost', 'enrol_stripepaymentpro').'</p>';
        }

        // Calculate localised and "." cost
        $localisedcost = format_float($total_cost, 2, true);

        if (isguestuser()) { // Force login only for guest user, not real users with guest role.
            if (empty($CFG->loginhttps)) {
                $wwwroot = $CFG->wwwroot;
            } else {
                $wwwroot = str_replace("http://", "https://", $CFG->wwwroot);
            }
            return '<div class="mdl-align"><p>'.get_string('paymentrequired').'</p>'.
                   '<p><b>'.get_string('cost').": $instance->currency $localisedcost".'</b></p>'.
                   '<p><a href="'.$wwwroot.'/login/">'.get_string('loginsite').'</a></p></div>';
        }

        // Sanitise some fields before building the Stripe form.
        $coursefullname = format_string($course->fullname, true, array('context' => $context));
        $instancename = $this->get_instance_name($instance);
        $enable_coupon_section = !empty(get_config('enrol_stripepayment', 'enable_coupon_section')) ? true : false;
        $enrolbtncolor = get_config('enrol_stripepayment', 'enrolbtncolor');
        $paymentgatewaytype = get_config('enrol_stripepaymentpro', 'payment_gateway_type');
        $publishablekey = get_config('enrol_stripepayment', 'publishablekey');

        // Prepare template data
        $templatedata = [
            'currency' => $instance->currency,
            'currency_symbol' => $currency_symbol,
            'cost' => $localisedcost,
            'total_cost' => format_float($total_cost, 2, true),
            'subtotal' => format_float($subtotal, 2, true),
            'sign_up_fee' => format_float($sign_up_fee, 2, true),
            'renewal_fee' => format_float($renewal_fee, 2, true),
            'coursename' => $coursefullname,
            'instanceid' => $instance->id,
            'enrolbtncolor' => $enrolbtncolor,
            'enable_coupon_section' => $enable_coupon_section,
            'has_recurring' => $instance->customtext4 > 0,
            'interval' => $interval,
            'interval_count' => $interval_count,
            'trial_period_days' => isset($trial_period_days) ? $trial_period_days : 0,
        ];

        // Start output buffering to capture the template output
        $body = $OUTPUT->render_from_template('enrol_stripepaymentpro/enrol_page', $templatedata);

        // Set up the required JavaScript for Stripe integration.
        $plugin = enrol_get_plugin('stripepaymentpro');
        $PAGE->requires->js_call_amd('enrol_stripepaymentpro/stripe_payment_pro', 'stripe_payment_pro',
            [
                $USER->id,
                null, // Couponid starts as null.
                $instance->id,
                $publishablekey,
                get_string("pleasewait", "enrol_stripepayment"),
                get_string("entercoupon", "enrol_stripepayment"),
                get_string("couponappling", "enrol_stripepayment"),
                get_string("paymenterror", "enrol_stripepayment"),
                $paymentgatewaytype,
            ]
        );
        $enrolpage = new enrol_page(
            instance: $instance,
            header: $instancename,
            body: $body
        );
        return $OUTPUT->render($enrolpage);
    }

    /**
	 * Convert an amount in the currency's base unit to its equivalent fractional unit.
	 *
	 * Stripe wants amounts in the fractional unit (e.g., pennies), not the base unit (e.g., dollars). Zero-decimal
	 * currencies are not included yet, see `$supported_currencies`.
	 *
	 * The data here comes from https://en.wikipedia.org/wiki/List_of_circulating_currencies.
	 *
	 * @param string $order_currency
	 *
	 * @return array
	 * @throws Exception
	 */
    public function get_fractional_unit_amount( $order_currency) {
        $multiplier_amount = null;

		$currency_multipliers = array(
			5    => array( 'MRO', 'MRU' ),
			100  => array(
				'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN',
				'BMD', 'BND', 'BOB', 'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD', 'CAD', 'CDF', 'CHF', 'CNY', 'COP',
				'CRC', 'CUC', 'CUP', 'CVE', 'CZK', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'EUR', 'FJD', 'FKP',
				'GBP', 'GEL', 'GGP', 'GHS', 'GIP', 'GMD', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR',
				'ILS', 'IMP', 'INR', 'IRR', 'ISK', 'JEP', 'JMD', 'JOD', 'KES', 'KGS', 'KHR', 'KPW', 'KYD', 'KZT',
				'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'MAD', 'MDL', 'MKD', 'MMK', 'MNT', 'MOP', 'MUR', 'MVR', 'MWK',
				'MXN', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'NZD', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR',
				'PLN', 'PRB', 'QAR', 'RON', 'RSD', 'RUB', 'SAR', 'SBD', 'SCR', 'SDG', 'SEK', 'SGD', 'SHP', 'SLL',
				'SOS', 'SRD', 'SSP', 'STD', 'SYP', 'SZL', 'THB', 'TJS', 'TMT', 'TOP', 'TRY', 'TTD', 'TVD', 'TWD',
				'TZS', 'UAH', 'UGX', 'USD', 'UYU', 'UZS', 'VEF', 'WST', 'XCD', 'YER', 'ZAR', 'ZMW',
			),
			1000 => array( 'BHD', 'IQD', 'KWD', 'LYD', 'OMR', 'TND' ),
		);

		foreach ( $currency_multipliers as $multiplier => $currencies ) {
			if ( in_array( $order_currency, $currencies, true ) ) {
                $multiplier_amount = $multiplier;
			}
		}

		if ( is_null( $multiplier_amount ) ) {
			throw new Exception( "Unknown currency multiplier for $order_currency." );
		}

		return  $multiplier_amount;
	}

    /**
     * get the coupon details and create the discount price object 
     * 
     * @param int $amount the initial amount for which the the discount will deduct
     * @param int $couponid in the coupon id getting from stripe  
     */
    public function create_discounted_price_object($discountedpriceobject, $amount, $coupon, $currency, $enrol) {
        $plugin = enrol_get_plugin('stripepaymentpro');
        // If there is one time cost then apply to the one time fees
        if($amount > 0){
            try {
                // Calculate the discounted amount
                if ($coupon->percent_off) {
                    $discounted_amount = $amount * (1 - $coupon->percent_off / 100);
                } elseif ($coupon->amount_off) {
                    $discounted_amount = $amount - $coupon->amount_off;
                } else {
                    $discounted_amount = $amount;
                }
                $discountedpriceobject =  Price::create([
                'unit_amount' => $discounted_amount,
                'currency' => $currency,
                'product'    => $enrol->customtext2,
                ]);

            } catch (Exception $e) {
                \core\notification::error($e->getMessage());
            }
        }
        return $discountedpriceobject;
    }

    /**
     * Detect if the current API key is in test mode
     * @param string $secretkey The Stripe secret key
     * @return bool True if test mode, false if live mode
     */
    private function is_test_mode($secretkey) {
        return strpos($secretkey, 'sk_test_') === 0;
    }

    /**
     * Safely retrieve a Stripe price with error handling for mode mismatches
     * @param string $price_id The Stripe price ID
     * @return object|null The price object or null if not found
     */
    private function safe_price_retrieve($price_id) {
        if (empty($price_id) || $price_id === '0') {
            return null;
        }

        try {
            return Price::retrieve($price_id);
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Price doesn't exist in current mode (likely switched between test/live)
            if (strpos($e->getMessage(), 'No such price') !== false) {
                debugging('Price ' . $price_id . ' not found in current Stripe mode. May need to recreate prices after mode switch.', DEBUG_DEVELOPER);
                return null;
            }
            throw $e; // Re-throw other errors
        }
    }

    /**
     * Check if prices need to be recreated due to API key mode change
     * @param stdClass $instance The enrolment instance
     * @return bool True if prices need recreation
     */
    private function prices_need_recreation($instance) {
        if (empty($instance->customtext3)) {
            return false; // No prices to check
        }

        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        if (empty($secretkey)) {
            return false;
        }

        Stripe::setApiKey($secretkey);

        // Try to retrieve the initial price
        $initial_price = $this->safe_price_retrieve($instance->customtext3);
        if (!$initial_price) {
            return true; // Initial price not found, needs recreation
        }

        // If there's a recurring price, check it too
        if (!empty($instance->customtext4) && $instance->customtext4 !== '0') {
            $recurring_price = $this->safe_price_retrieve($instance->customtext4);
            if (!$recurring_price) {
                return true; // Recurring price not found, needs recreation
            }
        }

        return false; // All prices found, no recreation needed
    }

    /**
     * Recreate Stripe prices for an instance after API key mode change
     * @param stdClass $instance The enrolment instance
     * @return bool True if recreation was successful
     */
    private function recreate_prices_for_instance($instance) {
        global $DB;

        $secretkey = get_config('enrol_stripepayment', 'secretkey');
        if (empty($secretkey)) {
            return false;
        }

        try {
            Stripe::setApiKey($secretkey);

            // Get or create product
            $productid = $instance->customtext2;
            if (!$productid) {
                // Create new product if none exists
                $course = $DB->get_record('course', ['id' => $instance->courseid]);
                $product = \Stripe\Product::create([
                    'name' => $course->fullname,
                    'description' => 'Course enrolment for ' . $course->fullname,
                ]);
                $productid = $product->id;

                // Update instance with product ID
                $DB->set_field('enrol', 'customtext2', $productid, ['id' => $instance->id]);
            }

            // Recreate initial price
            $initial_amount = $instance->cost * $this->get_fractional_unit_amount($instance->currency);
            $initial_price = Price::create([
                'unit_amount' => $initial_amount,
                'currency' => $instance->currency,
                'product' => $productid,
            ]);

            // Update instance with new initial price ID
            $DB->set_field('enrol', 'customtext3', $initial_price->id, ['id' => $instance->id]);

            // Recreate recurring price if needed
            if (!empty($instance->customtext4) && $instance->customtext4 !== '0') {
                // Get the recurring amount from the old price or use a default
                $recurring_amount = $instance->cost * $this->get_fractional_unit_amount($instance->currency); // Default to same as initial
                $intervals = $this->get_renewalintarval();
                $interval = $intervals[$instance->customint4] ?? 'month';

                $recurring_price = Price::create([
                    'unit_amount' => $recurring_amount,
                    'currency' => $instance->currency,
                    'recurring' => [
                        'interval' => $interval,
                        'interval_count' => $instance->customint1 ?? 1,
                    ],
                    'product' => $productid,
                ]);

                // Update instance with new recurring price ID
                $DB->set_field('enrol', 'customtext4', $recurring_price->id, ['id' => $instance->id]);
            }

            return true;

        } catch (Exception $e) {
            debugging('Failed to recreate prices for instance ' . $instance->id . ': ' . $e->getMessage(), DEBUG_DEVELOPER);
            return false;
        }
    }
}
